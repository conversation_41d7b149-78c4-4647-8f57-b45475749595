.sidebar {
    width: 135px;
    background-color: #ffffff;
    border-right: 1px solid #e9ecef;
    padding: 15px 0;
    min-height: calc(100vh - 90.99px); /* Ajustado para nova altura do header */
    height: calc(100vh - 90.99px); /* Ajustado para nova altura do header */
    overflow-y: auto;
    position: fixed;
    top: 90.99px; /* Ajustado para nova altura do header */
    left: 0;
}

.sidebar .menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar .menu li {
    padding: 15px 10px;
    border-bottom: 1px solid #f8f9fa;
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    text-align: center;
}

.sidebar .menu li svg {
    width: 2.2em;
    height: 2.2em;
    stroke: currentColor;
    margin-bottom: 5px;
}

.sidebar .menu li:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
}

.sidebar .menu li:first-child {
    background-color: #e8f4fd;
    color: var(--primary-color);
    border-right: 3px solid var(--primary-color);
}

.help-button {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
} 
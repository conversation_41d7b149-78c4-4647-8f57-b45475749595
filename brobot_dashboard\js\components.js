// Component loader utility
class ComponentLoader {
    static async loadComponent(componentPath, targetSelector) {
        try {
            const response = await fetch(componentPath);
            if (!response.ok) {
                throw new Error(`Failed to load component: ${componentPath}`);
            }
            const html = await response.text();
            const targetElement = document.querySelector(targetSelector);
            if (targetElement) {
                targetElement.innerHTML = html;
            }
        } catch (error) {
            console.error('Error loading component:', error);
        }
    }

    static async loadComponents() {
        // Load components in parallel for better performance
        const componentLoadPromises = [
            this.loadComponent('components/header.html', '#header-placeholder'),
            this.loadComponent('components/sidebar.html', '#sidebar-placeholder'),
            this.loadComponent('components/filters.html', '#filters-placeholder'),
            this.loadComponent('components/cards.html', '#cards-placeholder'),
            this.loadComponent('components/charts.html', '#charts-placeholder'),
            this.loadComponent('components/table.html', '#table-placeholder')
        ];

        try {
            await Promise.all(componentLoadPromises);
            console.log('All components loaded successfully');
        } catch (error) {
            console.error('Error loading components:', error);
        }
    }
}

// Load components when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    await ComponentLoader.loadComponents();
    
    // Initialize Vue app after components are loaded
    if (window.initializeVueApp) {
        window.initializeVueApp();
    }
}); 
.filters-section {
    background-color: transparent;
    padding: 0;
    margin-bottom: 20px;
    width: 100%;
}

.search-bar {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center;
    width: 100%;
}

.search-input-container {
    flex: 1 1 auto;
    position: relative;
    min-width: 0; /* Importante para permitir que o flex funcione corretamente */
}

.search-input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    font-family: 'Montserrat', sans-serif;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: #6c757d;
    pointer-events: none;
}

.filter-btn {
    flex: 0 0 auto; /* <PERSON><PERSON> cresce nem encolhe */
    width: 150px;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    display: flex;
    font-size: 12px;
    font-weight: 700;
    font-family: 'Montserrat', sans-serif;
    text-decoration: none;
    height: 35px;
    line-height: 26px;
    color: #192549;
    background-color: transparent;
    border: 1.5px solid #192549;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
    box-sizing: border-box; /* Garante que o padding não aumente a largura */
}

.filter-btn svg {
    width: 16px;
    height: 16px;
}

.tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.tabs button {
    padding: 8px 16px;
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 12px;
}

.tabs button:hover {
    background-color: var(--primary-color-hover);
}

.search-and-filters {
    display: flex;
    gap: 15px;
}

.filters-btn, .show-columns-btn, .sort-by select {
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: #fff;
    font-weight: 500;
    cursor: pointer;
} 
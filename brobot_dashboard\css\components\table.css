.fines-table-container {
    margin-top: 20px;
    background-color: #fff;
    border-radius: 5px;
    padding: 1em;
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
    overflow-x: auto;
}

.fines-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 1200px; /* Largura mínima para garantir que todas as colunas sejam visíveis */
}

.fines-table th, .fines-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
    white-space: nowrap;
}

/* Ajustes específicos para colunas */
.fines-table th:nth-child(8), .fines-table td:nth-child(8) {
    /* Coluna Descrição - permite quebra de linha */
    white-space: normal;
    max-width: 250px;
    word-wrap: break-word;
}

.fines-table th:last-child, .fines-table td:last-child {
    /* Coluna Opções - largura fixa */
    width: 120px;
    min-width: 120px;
}

.fines-table th {
    font-weight: 500;
    color: #86909C;
    cursor: pointer;
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10+ */
    user-select: none; /* Standard syntax */
}

.fines-table th:hover {
    color: var(--header-color);
}

.options-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    font-size: 12px;
    white-space: nowrap;
    transition: all 0.2s ease;
    min-width: 113px;
    text-align: center;
}

.options-btn:hover {
    background-color: var(--primary-color-hover);
}

.options-btn:active {
    background-color: var(--primary-color-active);
}

.ranking-section {
    margin-top: 40px;
    background-color: #fff;
    border-radius: 5px;
    padding: 1em;
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
}

.ranking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.ranking-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--header-color);
}

.ranking-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ranking-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.ranking-item:last-child {
    border-bottom: none;
}

.ranking-position {
    font-weight: bold;
    color: var(--brobot-blue);
    min-width: 30px;
}

.ranking-company {
    flex: 1;
    margin-left: 15px;
}

.company-name {
    font-weight: 500;
    color: var(--header-color);
    margin-bottom: 2px;
}

.company-cnpj {
    font-size: 12px;
    color: #86909C;
}

.ranking-stats {
    text-align: right;
}

.fine-count {
    font-weight: bold;
    color: var(--header-color);
}

.fine-value {
    font-size: 12px;
    color: #86909C;
}

.table-controls-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.table-controls-bar .left, .table-controls-bar .right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.table-info, .sort-by label {
    font-size: 14px;
    color: var(--text-color);
}

.select-all-btn {
    background-color: #fff;
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    text-transform: capitalize;
}

.status.vencido {
    background-color: #ffebeb;
    color: #dc3545;
}

.status.avencer { /* Assuming 'a vencer' status */
    background-color: #fff3cd;
    color: #ffc107;
} 
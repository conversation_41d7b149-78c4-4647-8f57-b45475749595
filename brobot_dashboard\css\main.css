* {
    box-sizing: border-box;
}

:root {
    --primary-color: #2d98da;
    --primary-color-hover: #4aaae5;
    --primary-color-active: #247db3;
    --primary-color-disabled: #9ecce8;
    --brobot-blue: #2d98da;
    --brobot-light-blue: #e8f4fd;
    --brobot-dark-blue: #247db3;
    --background-color: #f8f9fa;
    --sidebar-bg: #ffffff;
    --text-color: #051b30;
    --text-light: rgba(5,27,48,.48);
    --header-color: #051b30;
    --border-color: #dee2e6;
    --card-shadow: 0 4px 16px rgba(84,84,84,.25);
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --info-color: #318be3;
}

body {
    font-family: 'Montserrat', sans-serif;
    margin: 0;
    background-color: #f8f9fa;
    color: #212529;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    box-sizing: border-box;
}

#app {
    display: flex;
    width: 100%;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    padding: 20px;
    background-color: #f8f9fa;
    overflow-y: auto;
    margin-left: 135px;
    margin-top: 90.99px;
    min-height: calc(100vh - 90.99px);
}

.page-title {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    text-align: left;
    font-family: Montserrat, sans-serif;
    line-height: 1.2;
    font-size: 2rem;
    color: #051b30;
    box-sizing: border-box;
    font-weight: normal;
    margin-top: 40px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title-arrow {
    width: 16px;
    height: 16px;
    stroke: #051b30;
    stroke-width: 2;
    fill: none;
} 
# Brobot Dashboard - Dependências Python
# Este projeto é principalmente front-end, mas inclui algumas dependências úteis

# Servidor HTTP básico (já incluído no Python 3.3+)
# python -m http.server

# Dependências opcionais para desenvolvimento e funcionalidades extras:

# Flask - Para um servidor mais robusto (opcional)
Flask==2.3.3

# Pandas - Para manipulação de dados CSV (se necessário no futuro)
pandas==2.1.3

# Requests - Para requisições HTTP (se necessário no futuro)
requests==2.31.0

# Werkzeug - Utilitários WSGI (dependência do Flask)
Werkzeug==2.3.7

# MarkupSafe - Para templates seguros (dependência do Flask)
MarkupSafe==2.1.3

# Jinja2 - Engine de templates (dependência do Flask)
Jinja2==3.1.2

# Click - Para CLI tools (dependência do Flask)
click==8.1.7

# Blinker - Para sinais (dependência do Flask)
blinker==1.6.3

# itsdangerous - Para assinatura de dados (dependência do Flask)
itsdangerous==2.1.2 
<section class="charts" v-if="!loading && fines.length > 0">
    <div class="chart-container">
        <div class="chart-header">
            <h3>Multas por Descrição</h3>
            <select v-model="descChartType" @change="renderDescChart">
                <option value="bar">Barras</option>
                <option value="pie">Pizza</option>
                <option value="line">Linha</option>
            </select>
        </div>
        <div class="chart-canvas-wrapper">
            <canvas id="finesByDescriptionChart"></canvas>
        </div>
    </div>
    <div class="chart-container">
        <div class="chart-header">
            <h3>Multas por Órgão Autuador</h3>
            <select v-model="agencyChartType" @change="renderAgencyChart">
                <option value="pie">Pizza</option>
                <option value="bar">Barras</option>
                <option value="doughnut">Rosca</option>
            </select>
        </div>
        <div class="chart-canvas-wrapper">
            <canvas id="finesByAgencyChart"></canvas>
        </div>
    </div>
</section> 
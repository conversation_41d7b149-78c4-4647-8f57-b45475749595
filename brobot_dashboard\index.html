<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brobot - Dashboard de Multas</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/papaparse@5.4.1/papaparse.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Component CSS Files -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components/header.css">
    <link rel="stylesheet" href="css/components/sidebar.css">
    <link rel="stylesheet" href="css/components/filters.css">
    <link rel="stylesheet" href="css/components/cards.css">
    <link rel="stylesheet" href="css/components/charts.css">
    <link rel="stylesheet" href="css/components/table.css">
</head>
<body>
    <div id="app">
        <!-- Header Component Placeholder -->
        <div id="header-placeholder"></div>

        <!-- Sidebar Component Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Title -->
            <div class="page-title">
                <svg class="page-title-arrow" xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="19" y1="12" x2="5" y2="12"></line>
                    <polyline points="12 19 5 12 12 5"></polyline>
                </svg>
                CaçaMultas Embarcador
            </div>

            <!-- Filters Component Placeholder -->
            <div id="filters-placeholder"></div>

            <p v-if="loading">Carregando dados do CSV...</p>
            <p v-if="!loading && fines.length > 0">
                Total de {{ fines.length }} multas carregadas com sucesso.
            </p>
            <p v-if="error">{{ error }}</p>

            <!-- Cards Component Placeholder -->
            <div id="cards-placeholder"></div>

            <div v-if="error" style="color: red; background: #ffebeb; padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>Ocorreu um erro:</strong> {{ error }}
                <p>Verifique se o nome das colunas no CSV correspondem a 'Vencimento da Infração' e 'Status'.</p>
            </div>
            
            <!-- Charts Component Placeholder -->
            <div id="charts-placeholder"></div>

            <!-- Table Component Placeholder -->
            <div id="table-placeholder"></div>
        </main>
    </div>

    <!-- Component Loader -->
    <script src="js/components.js"></script>

    <!-- Vue.js Application -->
    <script>
        // Define Vue app initialization function globally
        window.initializeVueApp = function() {
            const { createApp } = Vue;

            createApp({
                data() {
                    return {
                        fines: [],
                        loading: true,
                        error: null,
                        descChartType: 'bar',
                        agencyChartType: 'pie',
                        descChartInstance: null,
                        agencyChartInstance: null,
                        alertPeriod: '30',
                        searchQuery: '',
                    }
                },
                watch: {
                    // Apenas observa os dados filtrados e atualiza os gráficos existentes.
                    filteredFines() {
                        this.updateCharts();
                    },
                    // Recria o gráfico de DESCRIÇÃO APENAS se o seu tipo mudar.
                    descChartType() {
                        // Adiciona um pequeno delay para evitar conflitos
                        setTimeout(() => {
                            this.renderDescChart();
                        }, 10);
                    },
                    // Recria o gráfico de ÓRGÃO APENAS se o seu tipo mudar.
                    agencyChartType() {
                        // Adiciona um pequeno delay para evitar conflitos
                        setTimeout(() => {
                            this.renderAgencyChart();
                        }, 10);
                    }
                },
                computed: {
                    filteredFines() {
                        if (!this.searchQuery) {
                            return this.fines;
                        }
                        const query = this.searchQuery.toLowerCase();
                        return this.fines.filter(fine => {
                            const ait = fine[0] || '';
                            const cnpj = fine[1] || '';
                            const plate = fine[2] || '';
                            return ait.toLowerCase().includes(query) ||
                                   cnpj.toLowerCase().includes(query) ||
                                   plate.toLowerCase().includes(query);
                        });
                    },
                    overdueFines() {
                        if (!this.filteredFines.length) return 0;
                        const now = new Date();
                        now.setHours(0, 0, 0, 0);
                        return this.filteredFines.filter(fine => {
                            const dueDate = this.parseDate(fine[12]);
                            return dueDate && dueDate < now && fine[16] !== 'Pago';
                        }).length;
                    },
                    openFines() {
                        if (!this.filteredFines.length) return 0;
                        return this.filteredFines.filter(fine => fine[16] === 'vencido' || fine[16] === 'a vencer').length;
                    },
                    expiringFines() {
                        if (!this.filteredFines.length) return 0;
                        const now = new Date();
                        now.setHours(0, 0, 0, 0);
                        const futureDate = new Date();
                        futureDate.setDate(now.getDate() + parseInt(this.alertPeriod));
                        return this.filteredFines.filter(fine => {
                            const dueDate = this.parseDate(fine[12]);
                            return dueDate && dueDate > now && dueDate <= futureDate;
                        }).length;
                    },
                    totalOpenValue() {
                        if (!this.filteredFines.length) return 'R$ 0,00';
                        const total = this.filteredFines
                            .filter(fine => fine[16] === 'vencido' || fine[16] === 'a vencer')
                            .reduce((sum, fine) => {
                                const valueString = fine[13] || '0';
                                const value = parseFloat(valueString.replace(',', '.'));
                                return sum + (isNaN(value) ? 0 : value);
                            }, 0);
                        return total.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
                    },
                    finesByDescription() {
                        if (!this.filteredFines.length) return {};
                        return this.filteredFines.reduce((acc, fine) => {
                            const description = fine[6] || 'Sem Descrição';
                            acc[description] = (acc[description] || 0) + 1;
                            return acc;
                        }, {});
                    },
                    finesByAgency() {
                         if (!this.filteredFines.length) return {};
                        return this.filteredFines.reduce((acc, fine) => {
                            const agency = fine[8] || 'Não identificado';
                            acc[agency] = (acc[agency] || 0) + 1;
                            return acc;
                        }, {});
                    },
                    topCNPJsByFines() {
                        if (!this.filteredFines.length) return [];

                        // Agrupa por CNPJ e conta as multas
                        const cnpjCounts = this.filteredFines.reduce((acc, fine) => {
                            const cnpj = fine[1] || 'Não identificado';
                            const companyName = fine[23] || 'Empresa não identificada'; // Nome da empresa está na coluna 23

                            if (!acc[cnpj]) {
                                acc[cnpj] = {
                                    cnpj: cnpj,
                                    companyName: companyName,
                                    count: 0,
                                    totalValue: 0
                                };
                            }

                            acc[cnpj].count += 1;

                            // Soma o valor das multas
                            const valueString = fine[13] || '0';
                            const value = parseFloat(valueString.replace(',', '.'));
                            acc[cnpj].totalValue += isNaN(value) ? 0 : value;

                            return acc;
                        }, {});

                        // Converte para array e ordena por quantidade de multas (decrescente)
                        return Object.values(cnpjCounts)
                            .sort((a, b) => b.count - a.count)
                            .slice(0, 10); // Top 10
                    }
                },
                mounted() {
                    this.loadFines();
                },
                methods: {
                    parseDate(dateString) {
                        if (!dateString || typeof dateString !== 'string') return null;
                        const parts = dateString.split('/');
                        if (parts.length === 3) {
                            // Formato DD/MM/AAAA
                            const [day, month, year] = parts;
                            return new Date(year, month - 1, day);
                        }
                        // Tenta outros formatos se necessário, ou retorna o parsing padrão
                        return new Date(dateString);
                    },
                    formatDate(dateString, withTime = false) {
                        const date = this.parseDate(dateString);
                        if (!date) return 'N/A';
                        const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
                        if (withTime) {
                            options.hour = '2-digit';
                            options.minute = '2-digit';
                        }
                        return date.toLocaleDateString('pt-BR', options);
                    },
                    formatCurrency(value) {
                        if (!value || isNaN(value)) return 'R$ 0,00';
                        return value.toLocaleString('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                        });
                    },
                    updateCharts() {
                        try {
                            // Atualiza Gráfico de Descrição se existir
                            if (this.descChartInstance && this.descChartInstance.data) {
                                this.descChartInstance.data.labels = Object.keys(this.finesByDescription);
                                this.descChartInstance.data.datasets[0].data = Object.values(this.finesByDescription);
                                this.descChartInstance.update();
                            }

                            // Atualiza Gráfico de Órgão se existir
                            if (this.agencyChartInstance && this.agencyChartInstance.data) {
                                this.agencyChartInstance.data.labels = Object.keys(this.finesByAgency);
                                this.agencyChartInstance.data.datasets[0].data = Object.values(this.finesByAgency);
                                this.agencyChartInstance.update();
                            }
                        } catch (error) {
                            console.error('Error updating charts:', error);
                            // Se houver erro, tenta recriar os gráficos
                            this.$nextTick(() => {
                                this.renderDescChart();
                                this.renderAgencyChart();
                            });
                        }
                    },
                    loadFines() {
                        Papa.parse('teste_20250626.csv', {
                            download: true,
                            header: false,
                            skipEmptyLines: true,
                            complete: (results) => {
                                if (results.errors.length > 0) {
                                    console.error('Erros de parsing:', results.errors);
                                    this.error = `Erros encontrados ao processar o CSV: ${results.errors[0].message}`;
                                    this.loading = false;
                                    return;
                                }
                                this.fines = results.data;
                                this.loading = false;
                                console.log('Dados carregados com sucesso:', this.fines);
                                this.$nextTick(() => {
                                    // Cria os gráficos pela primeira vez, de forma independente.
                                    this.renderDescChart(); 
                                    this.renderAgencyChart();
                                });
                            },
                            error: (err) => {
                                this.error = 'Falha ao carregar o arquivo CSV. Verifique o console.';
                                this.loading = false;
                                console.error(err);
                            }
                        });
                    },
                    renderDescChart() {
                        this.$nextTick(() => {
                            try {
                                // 1. Destroi o gráfico antigo se ele existir.
                                if(this.descChartInstance) {
                                    this.descChartInstance.destroy();
                                    this.descChartInstance = null;
                                }

                                // Aguarda um pouco para garantir que o DOM está estável
                                setTimeout(() => {
                                    // 2. Garante que o elemento canvas existe.
                                    const descCanvas = document.getElementById('finesByDescriptionChart');
                                    if (!descCanvas) {
                                        console.error('Canvas element finesByDescriptionChart not found');
                                        return;
                                    }

                                    // Verifica se o canvas está realmente no DOM
                                    if (!document.body.contains(descCanvas)) {
                                        console.error('Canvas element not in DOM');
                                        return;
                                    }

                                // 3. Constrói as opções corretas para o tipo de gráfico.
                                const sharedOptions = { responsive: true, maintainAspectRatio: false };
                                const descChartOptions = {
                                    ...sharedOptions,
                                    plugins: { legend: { display: true } },
                                    scales: {} // Começa com um objeto de escalas vazio
                                };

                                if (this.descChartType === 'bar') {
                                    descChartOptions.indexAxis = 'y';
                                    descChartOptions.scales = { x: { beginAtZero: true } };
                                    descChartOptions.plugins.legend.display = false;
                                } else if (this.descChartType === 'line') {
                                     descChartOptions.scales = { y: { beginAtZero: true } };
                                } else if (this.descChartType === 'pie') {
                                    delete descChartOptions.scales; // Remove escalas para pizza
                                }

                                // 4. Cria a nova instância do gráfico.
                                const descCtx = descCanvas.getContext('2d');
                                if (!descCtx) {
                                    console.error('Não foi possível obter o contexto 2D do canvas');
                                    return;
                                }
                                this.descChartInstance = new Chart(descCtx, {
                                    type: this.descChartType,
                                    data: {
                                        labels: Object.keys(this.finesByDescription),
                                        datasets: [{
                                            label: '# de Multas',
                                            data: Object.values(this.finesByDescription),
                                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                                            borderColor: 'rgba(54, 162, 235, 1)',
                                            borderWidth: 1
                                        }]
                                    },
                                    options: descChartOptions
                                });
                                }, 50); // Delay de 50ms
                            } catch (error) {
                                console.error('Error rendering description chart:', error);
                            }
                        });
                    },
                    renderAgencyChart() {
                        this.$nextTick(() => {
                            try {
                                // 1. Destroi o gráfico antigo se ele existir.
                                if(this.agencyChartInstance) {
                                    this.agencyChartInstance.destroy();
                                    this.agencyChartInstance = null;
                                }

                                // Aguarda um pouco para garantir que o DOM está estável
                                setTimeout(() => {
                                    // 2. Garante que o elemento canvas existe.
                                    const agencyCanvas = document.getElementById('finesByAgencyChart');
                                    if (!agencyCanvas) {
                                        console.error('Canvas element finesByAgencyChart not found');
                                        return;
                                    }

                                    // Verifica se o canvas está realmente no DOM
                                    if (!document.body.contains(agencyCanvas)) {
                                        console.error('Canvas element not in DOM');
                                        return;
                                    }

                                // 3. Constrói as opções corretas para o tipo de gráfico.
                                const sharedOptions = { responsive: true, maintainAspectRatio: false };
                                const agencyChartOptions = { ...sharedOptions };

                                if (this.agencyChartType === 'bar') {
                                    agencyChartOptions.scales = { y: { beginAtZero: true } };
                                }

                                // 4. Cria a nova instância do gráfico.
                                const agencyCtx = agencyCanvas.getContext('2d');
                                if (!agencyCtx) {
                                    console.error('Não foi possível obter o contexto 2D do canvas');
                                    return;
                                }
                                this.agencyChartInstance = new Chart(agencyCtx, {
                                    type: this.agencyChartType,
                                    data: {
                                        labels: Object.keys(this.finesByAgency),
                                        datasets: [{
                                            label: '# de Multas',
                                            data: Object.values(this.finesByAgency),
                                            backgroundColor: [
                                                'rgba(255, 99, 132, 0.6)',
                                                'rgba(54, 162, 235, 0.6)',
                                                'rgba(255, 206, 86, 0.6)',
                                                'rgba(75, 192, 192, 0.6)',
                                                'rgba(153, 102, 255, 0.6)'
                                            ],
                                        }]
                                    },
                                    options: agencyChartOptions
                                });
                                }, 50); // Delay de 50ms
                            } catch (error) {
                                console.error('Error rendering agency chart:', error);
                            }
                        });
                    },
                    // Método para limpar recursos quando necessário
                    cleanup() {
                        // Limpa timeouts pendentes
                        if (this.descChartRenderTimeout) {
                            clearTimeout(this.descChartRenderTimeout);
                            this.descChartRenderTimeout = null;
                        }
                        if (this.agencyChartRenderTimeout) {
                            clearTimeout(this.agencyChartRenderTimeout);
                            this.agencyChartRenderTimeout = null;
                        }

                        // Destroi instâncias de gráficos
                        if (this.descChartInstance) {
                            this.descChartInstance.stop();
                            this.descChartInstance.destroy();
                            this.descChartInstance = null;
                        }
                        if (this.agencyChartInstance) {
                            this.agencyChartInstance.stop();
                            this.agencyChartInstance.destroy();
                            this.agencyChartInstance = null;
                        }
                    }
                },
                // Lifecycle hook para limpeza
                beforeUnmount() {
                    this.cleanup();
                }
            }).mount('#app');
        };
    </script>
</body>
</html> 
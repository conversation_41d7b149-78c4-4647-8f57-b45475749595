.info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.card {
    background-color: #fff;
    padding: 1em;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
    transition: all 0.2s ease;
    position: relative;
}

.card:hover {
    box-shadow: 0 6px 20px rgba(84,84,84,.35);
}

.card .title {
    font-size: 14px;
    font-weight: 600;
    color: #465059;
    margin-bottom: 8px;
}

.card .value {
    font-size: 32px;
    font-weight: 700;
    color: var(--header-color);
    margin-top: 10px;
}

.card .value.danger {
    color: #dc3545;
}

.card .value.warning {
    color: #ffc107;
}

.card .value.info {
    color: #17a2b8;
}

.card .value.success {
    color: #28a745;
}

.card-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title-container select {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 5px;
} 
<!-- Ranking de CNPJs -->
<section class="ranking-section" v-if="!loading && fines.length > 0">
    <div class="ranking-header">
        <h3>⚠️ Top 10 CNPJs com Mais Multas</h3>
    </div>
    <ul class="ranking-list">
        <li class="ranking-item" v-for="(item, index) in topCNPJsByFines" :key="item.cnpj">
            <span class="ranking-position">{{ index + 1 }}º</span>
            <div class="ranking-company">
                <div class="company-name">{{ item.companyName }}</div>
                <div class="company-cnpj">CNPJ: {{ item.cnpj }}</div>
            </div>
            <div class="ranking-stats">
                <div class="fine-count">{{ item.count }} multas</div>
                <div class="fine-value">{{ formatCurrency(item.totalValue) }}</div>
            </div>
        </li>
    </ul>
</section>

<div class="table-controls-bar" v-if="!loading && fines.length > 0">
    <div class="left">
        <span class="table-info">Mostrando de 1 até {{ filteredFines.length }} de {{ fines.length }} registro(s)</span>
        <button class="select-all-btn">Selecionar todas ({{ filteredFines.length }})</button>
    </div>
    <div class="right">
        <div class="sort-by">
            <label>Ordenar por:</label>
            <select>
                <option>Data de atualização</option>
            </select>
        </div>
        <button class="show-columns-btn">Mostrar colunas</button>
    </div>
</div>

<!-- Tabela de Dados -->
<section class="fines-table-container" v-if="!loading && fines.length > 0">
    <table class="fines-table">
        <thead>
            <tr>
                <th><input type="checkbox" /></th>
                <th>AIT</th>
                <th>Placa</th>
                <th>CNPJ</th>
                <th>Status</th>
                <th>Data da Infração</th>
                <th>Venc. da Infração</th>
                <th>Valor</th>
                <th>Órgão Autuador</th>
                <th>Descrição</th>
                <th>Atualizado em</th>
                <th>Opções</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="fine in filteredFines">
                <td><input type="checkbox" /></td>
                <td>{{ fine[0] }}</td>
                <td>{{ fine[2] }}</td>
                <td>{{ fine[1] }}</td>
                <td><span class="status" :class="fine[16]">{{ fine[16] }}</span></td>
                <td>{{ formatDate(fine[10]) }}</td>
                <td>{{ formatDate(fine[12]) }}</td>
                <td>R$ {{ fine[13] }}</td>
                <td>{{ fine[8] }}</td>
                <td>{{ fine[6] }}</td>
                <td>{{ formatDate(fine[18], true) }}</td>
                <td><button class="options-btn">Opções &#9662;</button></td>
            </tr>
        </tbody>
    </table>
</section> 
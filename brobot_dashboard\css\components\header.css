.header {
    background-color: #ffffff;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 90.99px;
    overflow: visible; /* Garantindo que nada seja cortado */
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
    overflow: visible;
    position: relative; /* Para permitir posicionamento absoluto do logo */
}

.header-logo {
    height: 32px;
    width: 159.98px; /* Largura exata conforme solicitado */
    object-fit: contain;
    filter: brightness(0) saturate(100%) invert(27%) sepia(99%) saturate(1729%) hue-rotate(199deg) brightness(96%) contrast(88%);
    /* Filtro para deixar o logo com a cor azul do sistema: #2d98da */
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification-bell {
    width: 24px;
    height: 24px;
    cursor: pointer;
    color: #6c757d;
}

.company-selector {
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    background-color: #f8f9fa;
    color: #495057;
    font-size: 14px;
    cursor: pointer;
}

.header .breadcrumb {
    font-size: 16px;
    font-weight: 500;
    color: var(--header-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header .breadcrumb::before {
    content: "←";
    font-size: 18px;
    color: #6c757d;
} 
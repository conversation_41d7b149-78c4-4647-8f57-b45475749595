<section class="info-cards" v-if="!loading && fines.length > 0">
    <div class="card">
        <div class="title">Multas Vencidas</div>
        <div class="value danger">{{ overdueFines }}</div>
    </div>
    <div class="card">
        <div class="title">Multas em Aberto</div>
        <div class="value warning">{{ openFines }}</div>
    </div>
    <div class="card">
        <div class="card-title-container">
            <div class="title">Alertas (Próximos)</div>
            <select v-model="alertPeriod">
                <option value="30">30 dias</option>
                <option value="15">15 dias</option>
                <option value="7">7 dias</option>
            </select>
        </div>
        <div class="value info">{{ expiringFines }}</div>
    </div>
    <div class="card">
        <div class="title">Valor Total em Aberto</div>
        <div class="value success">{{ totalOpenValue }}</div>
    </div>
</section> 
.charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-top: 40px;
}

.chart-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.chart-header h3 {
    margin: 0;
    font-size: 18px;
}

.chart-canvas-wrapper {
    position: relative;
    height: 400px; /* Adjust height as needed */
}

.chart-header select {
    padding: 5px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
} 